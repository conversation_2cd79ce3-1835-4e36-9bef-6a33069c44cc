<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <decisions>
        <name>AAR_Exist</name>
        <label>AAR Exist</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>AAR_Exist_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>retrive_AAR</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>retrive_status_kpi</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>gia_Cliente</name>
        <label>già Cliente?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Update_to_Non_contraente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>gia_Cliente_yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>AssetKPI.Value__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cliente</stringValue>
                </rightValue>
            </conditions>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Insert_to_Cliente</name>
        <label>Insert to Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Insert_Status_Non_contraente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Insert_to_Cliente_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Role</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Contraente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Insert_Status_Cliente</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>KPI_Exist</name>
        <label>KPI Exist</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Get_Cliente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>KPI_Exist_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>AssetKPI</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>update_to_Cliente</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>update_to_Cliente</name>
        <label>update to Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>gia_Cliente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>update_to_Cliente_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Role</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <stringValue>Contraente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_KPI_to_Cliente</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <description>vers prec 4
Logica stato cliente su Opportunity</description>
    <environments>Default</environments>
    <formulas>
        <description>[CODICE_FISCALE]_[AGENZIA]_[SOCIETA]_PP_AGENZIA_SOCIETA_STATO</description>
        <name>KPI_Ext_key</name>
        <dataType>String</dataType>
        <expression>{!Get_Cliente.ExternalId__c}+&quot;_&quot;+{!Get_Agency.ExternalId__c}+&quot;_&quot;+{!Get_Society.ExternalId__c}+&quot;_&quot;+&quot;PP_STATO&quot;</expression>
    </formulas>
    <interviewLabel>InsurancePolicyParticipant - Update Stato Soggetto {!$Flow.CurrentDateTime}</interviewLabel>
    <label>InsurancePolicyParticipant - Update Stato Soggetto</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <description>[CODICE_FISCALE]_[AGENZIA]_[SOCIETA]_PP_AGENZIA_SOCIETA_STATO</description>
        <name>Insert_Status_Cliente</name>
        <label>Insert Status Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Copy_1_of_Update_Opportunity_Stato_Cliente</targetReference>
        </connector>
        <inputAssignments>
            <field>ExternalId__c</field>
            <value>
                <elementReference>KPI_Ext_key</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Key__c</field>
            <value>
                <stringValue>PP_AGENZIA_SOCIETA_STATO</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MasterRecordId__c</field>
            <value>
                <elementReference>retrive_AAR.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>PP_AGENZIA_SOCIETA_STATO</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_RecordType.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Value__c</field>
            <value>
                <stringValue>Cliente</stringValue>
            </value>
        </inputAssignments>
        <object>Asset</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <description>[CODICE_FISCALE]_[AGENZIA]_[SOCIETA]_PP_AGENZIA_SOCIETA_STATO</description>
        <name>Insert_Status_Non_contraente</name>
        <label>Insert Status Non contraente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Copy_1_of_Update_Opportunity_non_contraente</targetReference>
        </connector>
        <inputAssignments>
            <field>ExternalId__c</field>
            <value>
                <elementReference>KPI_Ext_key</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Key__c</field>
            <value>
                <stringValue>PP_AGENZIA_SOCIETA_STATO</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MasterRecordId__c</field>
            <value>
                <elementReference>retrive_AAR.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>PP_AGENZIA_SOCIETA_STATO</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>Get_RecordType.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Value__c</field>
            <value>
                <stringValue>Non contraente</stringValue>
            </value>
        </inputAssignments>
        <object>Asset</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Agency</name>
        <label>Get Agency</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Society</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Retrive_Insurance_Policy.Agency__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Cliente</name>
        <label>Get Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Agency</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.PrimaryParticipantAccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_RecordType</name>
        <label>Get RecordType</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Insert_to_Cliente</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Agency</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Society</name>
        <label>Get Society</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_RecordType</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Retrive_Insurance_Policy.Society__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>retrive_AAR</name>
        <label>retrive Agency AAR</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>AAR_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FinServ__RelatedAccount__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Retrive_Insurance_Policy.Agency__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>FinServ__Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.PrimaryParticipantAccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FinServ__AccountAccountRelation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Retrive_Insurance_Policy</name>
        <label>Retrive Insurance Policy</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>retrive_AAR</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.InsurancePolicyId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>InsurancePolicy</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>retrive_status_kpi</name>
        <label>retrive status kpi</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>true</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>KPI_Exist</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>MasterRecordId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>retrive_AAR.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Key__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>PP_AGENZIA_SOCIETA_STATO</stringValue>
            </value>
        </filters>
        <object>Asset</object>
        <outputReference>AssetKPI</outputReference>
        <queriedFields>Id</queriedFields>
        <queriedFields>Key__c</queriedFields>
        <queriedFields>Value__c</queriedFields>
        <queriedFields>ExternalId__c</queriedFields>
        <queriedFields>MasterRecordId__c</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>Copy_1_of_Update_Opportunity_non_contraente</name>
        <label>Copy 1 of Update Opportunity non contraente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.PrimaryParticipantAccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>AccountAgencyStatus__c</field>
            <value>
                <stringValue>Non contraente</stringValue>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Copy_1_of_Update_Opportunity_Stato_Cliente</name>
        <label>Copy 1 of Update Opportunity Stato Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.PrimaryParticipantAccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>AccountAgencyStatus__c</field>
            <value>
                <stringValue>Cliente</stringValue>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_KPI_to_Cliente</name>
        <label>Update KPI to Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Opportunity_Stato_Cliente</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AssetKPI.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Value__c</field>
            <value>
                <stringValue>Cliente</stringValue>
            </value>
        </inputAssignments>
        <object>Asset</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Opportunity_non_contraente</name>
        <label>Update Opportunity non contraente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.PrimaryParticipantAccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>AccountAgencyStatus__c</field>
            <value>
                <stringValue>Non contraente</stringValue>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Opportunity_Stato_Cliente</name>
        <label>Update Opportunity Stato Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.PrimaryParticipantAccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>AccountAgencyStatus__c</field>
            <value>
                <stringValue>Cliente</stringValue>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_to_Non_contraente</name>
        <label>Update to Non contraente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_Opportunity_non_contraente</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AssetKPI.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Value__c</field>
            <value>
                <stringValue>Non contraente</stringValue>
            </value>
        </inputAssignments>
        <object>Asset</object>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <object>InsurancePolicyParticipant</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Retrive_Insurance_Policy</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>AssetKPI</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Asset</objectType>
    </variables>
</Flow>
