<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>urcs_CaseEditEmailChiamante</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
    NOT(ISNEW()),
    ISCHANGED(EmailChiamante__c),
    OR(
        RecordType.DeveloperName == "ur_CasePQ",
        RecordType.DeveloperName == "ur_CaseSitoWeb",
        RecordType.DeveloperName == "ur_CaseAR"
    ),
    OR(
        CASESAFEID($User.Id) != UtAssegnatario__c,
        isClosed = true
    )
)</errorConditionFormula>
    <errorDisplayField>EmailChiamante__c</errorDisplayField>
    <errorMessage>Non è possibile modificare il valore questo campo.</errorMessage>
</ValidationRule>
